/**
 * 教师管理 API 服务
 * 提供教师相关的 API 调用接口
 */

import { createApiHeaders } from '@/lib/apiUtils';
import { PaginatedApiResponse } from '@/types';
import {
  CreateTeacherParams,
  CreateTeacherRequest,
  FindAllParams,
  PageAllTeacherParams,
  Teacher,
  TeacherDetailVO,
  TeacherListVO,
  TeacherSearchParams,
  TeacherSummary,
  UpdateTeacherParams,
} from '@/types/teacher';
import apiClient from './apiClient';

// API response wrapper
export interface ApiResponse<T> {
  success: boolean;
  code: number;
  message?: string;
  data: T;
  meta?: any;
}

// Paginated response wrapper
export interface PaginatedResponse<T> {
  data: T[];
  page: number;
  page_size: number;
  total: number;
  total_pages: number;
}

// 基础API路径
const Teacher_API = '/api/v1/tenants';


// Teacher API service
export const teachersApi = {
  /**
   * 获取教师简要信息列表（用于下拉选择）
   */
  async getTeacherSummaries(tenantName: string, isActive?: boolean): Promise<ApiResponse<TeacherSummary[]>> {
    return await apiClient.post(`${Teacher_API}/${tenantName}/teachers/Summaries`, isActive !== undefined ? { is_active: isActive } : {});
  },

  /**
   * 作者：张瀚
   * 说明：创建班级
   */
  findAll: async (tenantName: string, params: FindAllParams): Promise<ApiResponse<Teacher>> => {
    return apiClient.post(`/api/v1/tenants/${tenantName}/teachers/findAll`, params);
  },
  /**
   * 作者：张瀚
   * 说明：分页查询所有学生
   */
  pageAllTeacher: async (tenant_id: string, tenant_name: string, params: PageAllTeacherParams): Promise<PaginatedApiResponse<Teacher>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/students/pageAllTeacher`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },

  /**
   * 作者：张瀚
   * 说明：新建老师
   */
  createTeacher: async (tenant_id: string, tenant_name: string, params: CreateTeacherParams): Promise<ApiResponse<Teacher>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/students/createTeacher`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：新建老师
   */
  updateTeacher: async (tenant_id: string, tenant_name: string, params: CreateTeacherParams): Promise<ApiResponse<Teacher>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/students/updateTeacher`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },

  // Get all teachers with optional search parameters
  async getTeachers(params?: TeacherSearchParams): Promise<ApiResponse<PaginatedResponse<TeacherListVO>>> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '' && value !== 0) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const queryString = queryParams.toString();
    const url = queryString ? `${Teacher_API}?${queryString}` : '${Teacher_API}';
    return apiClient.get<ApiResponse<PaginatedResponse<TeacherListVO>>>(url);
  },

  // Get teacher by ID
  async getTeacherById(id: string): Promise<ApiResponse<Teacher>> {
    return apiClient.get<ApiResponse<Teacher>>(`${Teacher_API}/${id}`);
  },

  // Get teacher detail with related information
  async getTeacherDetail(id: string): Promise<ApiResponse<TeacherDetailVO>> {
    return apiClient.get<ApiResponse<TeacherDetailVO>>(`${Teacher_API}/${id}/detail`);
  },

  // Delete a teacher
  async deleteTeacher(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<ApiResponse<void>>(`${Teacher_API}/${id}`);
  },

  // Toggle teacher status (active/inactive)
  async toggleTeacherStatus(id: string, isActive: boolean): Promise<ApiResponse<Teacher>> {
    return apiClient.patch<ApiResponse<Teacher>>(`${Teacher_API}/${id}/toggle-status`, {
      is_active: isActive,
    });
  },

  // Check if employee ID is available (for creation)
  async checkEmployeeIdAvailability(employeeId: string): Promise<ApiResponse<{ is_available: boolean }>> {
    return apiClient.get<ApiResponse<{ is_available: boolean }>>(
      `${Teacher_API}/check-employee-id/${encodeURIComponent(employeeId)}`
    );
  },

  // Get teachers by employment status
  async getTeachersByStatus(status: string, params?: Omit<TeacherSearchParams, 'employment_status'>): Promise<ApiResponse<PaginatedResponse<TeacherListVO>>> {
    return this.getTeachers({ ...params, employment_status: status });
  },

  // Get teachers by subject group
  async getTeachersBySubjectGroup(subjectGroupId: number, params?: Omit<TeacherSearchParams, 'subject_group_id'>): Promise<ApiResponse<PaginatedResponse<TeacherListVO>>> {
    return this.getTeachers({ ...params, subject_group_id: subjectGroupId });
  },

  // Get teachers by grade level
  async getTeachersByGradeLevel(gradeLevelId: number, params?: Omit<TeacherSearchParams, 'grade_level_id'>): Promise<ApiResponse<PaginatedResponse<TeacherListVO>>> {
    return this.getTeachers({ ...params, grade_level_id: gradeLevelId });
  },

  // Get teachers who teach a specific subject
  async getTeachersBySubject(subject: string, params?: Omit<TeacherSearchParams, 'teaching_subject'>): Promise<ApiResponse<PaginatedResponse<TeacherListVO>>> {
    return this.getTeachers({ ...params, teaching_subject: subject });
  },

  // Get active teachers only
  async getActiveTeachers(params?: Omit<TeacherSearchParams, 'is_active'>): Promise<ApiResponse<PaginatedResponse<TeacherListVO>>> {
    return this.getTeachers({ ...params, is_active: true });
  },

  // Get teachers count by employment status
  async getTeachersCountByStatus(): Promise<ApiResponse<Record<string, number>>> {
    return apiClient.get<ApiResponse<Record<string, number>>>('${Teacher_API}/count/by-status');
  },

  // Get teachers count by subject group
  async getTeachersCountBySubjectGroup(): Promise<ApiResponse<Record<string, number>>> {
    return apiClient.get<ApiResponse<Record<string, number>>>('${Teacher_API}/count/by-subject-group');
  },

  // Export teachers data
  async exportTeachers(params?: TeacherSearchParams): Promise<Blob> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '' && value !== 0) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const queryString = queryParams.toString();
    const url = queryString ? `${Teacher_API}/export?${queryString}` : '${Teacher_API}/export';
    const response = await apiClient.get(url, {
      responseType: 'blob',
    });

    return response.data;
  },

  // Batch operations
  async batchUpdateTeachers(updates: Array<{ id: string; data: UpdateTeacherParams }>): Promise<ApiResponse<Teacher[]>> {
    return apiClient.post<ApiResponse<Teacher[]>>('${Teacher_API}/batch-update', { updates });
  },

  async batchDeleteTeachers(ids: string[]): Promise<ApiResponse<void>> {
    return apiClient.post<ApiResponse<void>>('${Teacher_API}/batch-delete', { ids });
  },

  async batchToggleStatus(ids: string[], isActive: boolean): Promise<ApiResponse<Teacher[]>> {
    return apiClient.post<ApiResponse<Teacher[]>>('${Teacher_API}/batch-toggle-status', {
      ids,
      is_active: isActive
    });
  },

};

// 导出默认教师API
export default teachersApi;

// 导出为 teacherApi 以保持一致性
export const teacherApi = teachersApi;