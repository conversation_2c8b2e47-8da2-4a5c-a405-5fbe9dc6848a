import React, { useState, useEffect } from 'react';
import {MultiSelect, Option} from '@/components/ui/multi-select';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { roleApi } from '@/services/roleApi';
import {Role, RoleCategory, ROLE_CATEGORY_LABELS} from '@/types/role';
import { toast } from 'sonner';

interface RoleOption extends Option{
  value: string;
  label: string;
  category: string; // 改为 string 类型以兼容 Option 的索引签名
  roleLevel: string; // 改为 string 类型以兼容 Option 的索引签名
  disable?: boolean;
}

interface RoleMultiSelectProps {
  value?: string[];
  onChange: (roleIds: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  label?: string;
  description?: string;
  maxSelected?: number;
  className?: string;
  tenantId?: string;
  excludeSystemRoles?: boolean;
  includeCategories?: RoleCategory[];
}

const RoleMultiSelect: React.FC<RoleMultiSelectProps> = ({
  value = [],
  onChange,
  placeholder = "选择角色...",
  disabled = false,
  label = "角色配置",
  description,
  maxSelected,
  className,
  tenantId,
  excludeSystemRoles = false,
  includeCategories,
}) => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [roleOptions, setRoleOptions] = useState<RoleOption[]>([]);
  const [selectedOptions, setSelectedOptions] = useState<RoleOption[]>([]);

  // 加载角色列表
  const loadRoles = async () => {
    try {
      setLoading(true);
      const response = await roleApi.getRoles({
        page: 1,
        page_size: 1000,
        is_active: true,
        tenant_id: tenantId,
      });

      if (response.success && response.data) {
        let filteredRoles = response.data || [];

        // 确保 filteredRoles 是数组
        if (!Array.isArray(filteredRoles)) {
          console.warn('角色数据格式错误，期望数组但收到:', typeof filteredRoles);
          filteredRoles = [];
        }

        // 过滤系统角色
        if (excludeSystemRoles) {
          filteredRoles = filteredRoles.filter(role => !role.is_system);
        }

        // 按分类过滤
        if (includeCategories && includeCategories.length > 0) {
          filteredRoles = filteredRoles.filter(role =>
            includeCategories.includes(role.category)
          );
        }

        setRoles(filteredRoles);
      } else {
        console.warn('角色API响应格式错误:', response);
        setRoles([]);
      }
    } catch (error) {
      console.error('Failed to load roles:', error);
      toast.error('加载角色列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 转换角色为选项格式
  useEffect(() => {
    if (!roles || !Array.isArray(roles)) {
      setRoleOptions([]);
      return;
    }

    const options: RoleOption[] = roles.map(role => ({
      value: role.id,
      label: `${role.name} (${ROLE_CATEGORY_LABELS[role.category]})`,
      category: role.category,
      roleLevel: role.level.toString(),
      disable: disabled,
    }));

    // 按分类和级别排序
    options.sort((a, b) => {
      if (a.category !== b.category) {
        return a.category.localeCompare(b.category);
      }
      return parseInt(a.roleLevel) - parseInt(b.roleLevel);
    });
    console.log("roles: ",options);
    setRoleOptions(options);
  }, [roles, disabled]);

  // 更新选中的选项
  useEffect(() => {
    const selected = roleOptions.filter(option => value.includes(option.value));
    setSelectedOptions(selected);
  }, [value, roleOptions]);

  // 初始化加载
  useEffect(() => {
    loadRoles();
  }, [tenantId, excludeSystemRoles, includeCategories]);

  // 处理选择变化
  const handleSelectionChange = (options: Option[]) => {
    const roleIds = options.map(option => option.value);
    onChange(roleIds);
  };



  return (
    <div className={className}>
      {label && (
        <div className="space-y-1 mb-2">
          <Label>{label}</Label>
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
      )}

      <MultiSelect
        value={selectedOptions}
        onChange={handleSelectionChange}
        options={roleOptions}
        placeholder={loading ? "加载中..." : placeholder}
        disabled={disabled || loading}
        maxSelected={maxSelected}
        groupBy="category"
        className="w-full"
        badgeClassName="bg-blue-100 text-blue-800 hover:bg-blue-200"
        loadingIndicator={
          <div className="flex items-center justify-center py-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-sm text-muted-foreground">加载角色...</span>
          </div>
        }
        emptyIndicator={
          <div className="text-center py-4 text-sm text-muted-foreground">
            {loading ? "加载中..." : "没有可用的角色"}
          </div>
        }
        onMaxSelected={(limit) => {
          toast.warning(`最多只能选择 ${limit} 个角色`);
        }}
      />

      {/* 显示选中的角色信息 */}
      {selectedOptions.length > 0 && (
        <div className="mt-2">
          <div className="text-xs text-muted-foreground mb-1">
            已选择 {selectedOptions.length} 个角色
          </div>
          <div className="flex flex-wrap gap-1">
            {selectedOptions.map((option) => {
              const role = roles.find(r => r.id === option.value);
              return (
                <Badge 
                  key={option.value} 
                  variant="secondary" 
                  className="text-xs"
                  title={role?.description}
                >
                  {role?.name}
                </Badge>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default RoleMultiSelect;
