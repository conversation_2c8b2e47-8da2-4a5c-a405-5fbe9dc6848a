import { PageParams } from ".";

export interface Teacher {
  id: string;
  tenant_id: string;
  user_id: string;
  employee_id: string;
  name: string;
  phone?: string;
  email?: string;
  gender?: string;
  date_of_birth?: string;
  id_card_number?: string;
  highest_education?: string;
  graduation_school?: string;
  major?: string;
  hire_date?: string;
  employment_status: string;
  title?: string;
  teaching_subjects?: string[];
  homeroom_class_id?: number;
  grade_level_id?: number;
  subject_group_id?: number;
  office_location?: string;
  bio?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface TeacherSummary {
  id: string;
  name: string;
  is_active: boolean;
}

export interface TeacherSearchParams {
  page?: number;
  page_size?: number;
  name?: string;
  employee_id?: string;
  employment_status?: string;
  teaching_subject?: string;
  grade_level_id?: number;
  subject_group_id?: number;
  is_active?: boolean;
}

export interface TeacherDetailVO {
  id: string;
  tenant_id: string;
  user_id: string;
  employee_id: string;
  name: string;
  phone?: string;
  email?: string;
  gender?: string;
  date_of_birth?: string;
  id_card_number?: string;
  highest_education?: string;
  graduation_school?: string;
  major?: string;
  hire_date?: string;
  employment_status: string;
  title?: string;
  teaching_subjects?: string[];
  homeroom_class_id?: number;
  homeroom_class_name?: string;
  grade_level_id?: number;
  grade_level_name?: string;
  subject_group_id?: number;
  subject_group_name?: string;
  office_location?: string;
  bio?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface TeacherListVO {
  id: string;
  employee_id: string;
  name: string;
  phone?: string;
  employment_status: string;
  title?: string;
  teaching_subjects?: string[];
  homeroom_class_name?: string;
  grade_level_name?: string;
  is_active: boolean;
}

// Form validation schemas
export interface TeacherFormData extends CreateTeacherRequest {
  id?: string;
}

export interface TeacherFormErrors {
  employee_id?: string;
  name?: string;
  phone?: string;
  email?: string;
  id_card_number?: string;
  hire_date?: string;
  employment_status?: string;
  user_id?: string;
}

// Component prop types
export interface TeacherTableProps {
  teachers: TeacherListVO[];
  loading?: boolean;
  onEdit: (teacher: TeacherListVO) => void;
  onDelete: (id: string) => void;
  onViewDetail: (teacher: TeacherListVO) => void;
  onToggleStatus: (id: string, isActive: boolean) => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
}

export interface TeacherFormProps {
  teacher?: Teacher;
  open: boolean;
  onClose: () => void;
  onSubmit: (data: TeacherFormData) => Promise<void>;
  loading?: boolean;
}

export interface TeacherDetailProps {
  teacher?: TeacherDetailVO;
  open: boolean;
  onClose: () => void;
  loading?: boolean;
}

export interface TeacherSelectProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  employmentStatus?: string;
  subjectGroupId?: number;
  className?: string;
}

// Filter and sort options
export const EMPLOYMENT_STATUS_OPTIONS = [
  { value: '在职', label: '在职' },
  { value: '离职', label: '离职' },
  { value: '退休', label: '退休' },
  { value: '停职', label: '停职' },
  { value: '试用期', label: '试用期' },
] as const;

export const GENDER_OPTIONS = [
  { value: '男', label: '男' },
  { value: '女', label: '女' },
  { value: '未知', label: '未知' },
] as const;

export const EDUCATION_LEVELS = [
  { value: '高中', label: '高中' },
  { value: '中专', label: '中专' },
  { value: '大专', label: '大专' },
  { value: '本科', label: '本科' },
  { value: '硕士', label: '硕士研究生' },
  { value: '博士', label: '博士研究生' },
  { value: '其他', label: '其他' },
] as const;

export const TEACHER_TITLES = [
  { value: '助教', label: '助教' },
  { value: '讲师', label: '讲师' },
  { value: '副教授', label: '副教授' },
  { value: '教授', label: '教授' },
  { value: '高级教师', label: '高级教师' },
  { value: '一级教师', label: '一级教师' },
  { value: '二级教师', label: '二级教师' },
  { value: '三级教师', label: '三级教师' },
  { value: '其他', label: '其他' },
] as const;

export const TEACHING_SUBJECTS_OPTIONS = [
  { value: '语文', label: '语文' },
  { value: '数学', label: '数学' },
  { value: '英语', label: '英语' },
  { value: '物理', label: '物理' },
  { value: '化学', label: '化学' },
  { value: '生物', label: '生物' },
  { value: '政治', label: '政治' },
  { value: '历史', label: '历史' },
  { value: '地理', label: '地理' },
  { value: '体育', label: '体育' },
  { value: '美术', label: '美术' },
  { value: '音乐', label: '音乐' },
  { value: '信息技术', label: '信息技术' },
  { value: '通用技术', label: '通用技术' },
  { value: '心理健康', label: '心理健康' },
  { value: '其他', label: '其他' },
] as const;

// Default values
export const DEFAULT_TEACHER_SEARCH: Required<TeacherSearchParams> = {
  page: 1,
  page_size: 20,
  name: '',
  employee_id: '',
  employment_status: '',
  teaching_subject: '',
  grade_level_id: 0,
  subject_group_id: 0,
  is_active: true,
};

export interface FindAllParams {
  name_like?: String,
}

export interface PageAllTeacherParams {
  name_like?: String,
  phone?: String,
  page_params: PageParams,
}

export interface CreateTeacherParams {
  user_id: string;
  employee_id: string;
  name: string;
  phone?: string;
  email?: string;
  gender?: string;
  date_of_birth?: string;
  id_card_number?: string;
  highest_education?: string;
  graduation_school?: string;
  major?: string;
  hire_date?: string;
  employment_status?: string;
  title?: string;
  teaching_subjects?: string[];
  homeroom_class_id?: number;
  grade_level_id?: number;
  subject_group_id?: number;
  office_location?: string;
  bio?: string;
}

export interface UpdateTeacherParams {
  user_id: String,
  employee_id?: string;
  name?: string;
  phone?: string;
  email?: string;
  gender?: string;
  date_of_birth?: string;
  id_card_number?: string;
  highest_education?: string;
  graduation_school?: string;
  major?: string;
  hire_date?: string;
  employment_status?: string;
  title?: string;
  teaching_subjects?: string[];
  homeroom_class_id?: number;
  grade_level_id?: number;
  subject_group_id?: number;
  office_location?: string;
  bio?: string;
  is_active?: boolean;
}
