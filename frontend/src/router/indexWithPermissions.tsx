import { createBrowserRouter } from "react-router-dom";
import RootLayoutWithPermissions from "@/layouts/RootLayoutWithPermissions";
import LoginPage from "@/pages/LoginPage";
import IdentitySelectPage from "@/pages/Identity/IdentitySelectPage";
import IdentityBindingPage from "@/pages/Identity/IdentityBindingPage";

// Import page components
import AdministrativeClassesDetailPage from "@/pages/Class/AdministrativeClassesDetailPage";
import AdministrativeClassesPage from "@/pages/Class/AdministrativeClassesPage";
import TeachingClassesPage from "@/pages/Class/TeachingClassesPage";
import TeachingClassesDetailPage from "@/pages/Class/TeachingClassDetailPage";
import ExamManagementPage from "@/pages/ExamManagement";
import GradeManagementPage from "@/pages/GradeManagement";
import GradingCenterPage from "@/pages/GradingCenter";
import { HomeworkManagementPage } from "@/pages/Homework";
import HomeworkDetailPage from "@/pages/Homework/HomeworkDetailPage";
import { RoleManagementPage } from "@/pages/RoleManagement";
import StatisticsPage from "@/pages/Statistics";
import { StudentManagementPage } from "@/pages/StudentManagement";
import { SubjectGroupsManagementPage } from "@/pages/SubjectGroupsManagement";
import { SubjectManagementPage } from "@/pages/SubjectManagement";
import { TeacherManagementPage } from "@/pages/TeacherManagement";
import { EducationalStageManagementPage } from "@/pages/EducationalStageManagement";
import TeachingAidsPage from "@/pages/TeachingAids";
import TeachingAidTextbookPreviewPage from "@/pages/TeachingAids/Preview";
import TenantManagementPage from "@/pages/Tenant/TenantManagementPage";
import UserManagementPage from "@/pages/UserManagement";

// Import enhanced protected route components
import {
  BaseProtectedRoute,
  PermissionProtectedRoute,
  MenuProtectedRoute,
  AdminProtectedRoute,
  CombinedProtectedRoute
} from "./ProtectedRoutes";
import CasbinPolicyManagementPage from "@/pages/CasbinPolicyManagement/CasbinPolicyManagementPage.tsx";
import { MenuManagementPage } from "@/pages/MenuManagement";

export const routerWithPermissions = createBrowserRouter([
  {
    path: "/login",
    element: <LoginPage />,
  },
  {
    path: "/identitySelect",
    element: <IdentitySelectPage />,
  },
  {
    path: "/identityBinding",
    element: <IdentityBindingPage />,
  },
  {
    path: "/",
    element: <RootLayoutWithPermissions />,
    children: [
      {
        element: <BaseProtectedRoute />,
        children: [
          // 默认首页 - 阅卷中心
          { 
            index: true, 
            element: (
              <MenuProtectedRoute menuId="grading_center">
                <GradingCenterPage />
              </MenuProtectedRoute>
            )
          },

          // 教学辅助管理
          { 
            path: "teaching-aids", 
            element: (
              <PermissionProtectedRoute>
                <TeachingAidsPage />
              </PermissionProtectedRoute>
            )
          },
          { 
            path: "teaching-aids/textbook/:id", 
            element: (
              <PermissionProtectedRoute>
                <TeachingAidTextbookPreviewPage />
              </PermissionProtectedRoute>
            )
          },

          // 考试管理
          { 
            path: "exam-management", 
            element: (
              <CombinedProtectedRoute 
                menuId="exam_management"
              >
                <ExamManagementPage />
              </CombinedProtectedRoute>
            )
          },

          // 作业管理
          { 
            path: "homework-management", 
            element: (
              <PermissionProtectedRoute>
                <HomeworkManagementPage />
              </PermissionProtectedRoute>
            )
          },
          // {
          //   path: "homeworks",
          //   element: (
          //     <PermissionProtectedRoute>
          //       <HomeworkListPage />
          //     </PermissionProtectedRoute>
          //   )
          // },
          // {
          //   path: "homeworks/new",
          //   element: (
          //     <PermissionProtectedRoute>
          //       <HomeworkForm />
          //     </PermissionProtectedRoute>
          //   )
          // },
          { 
            path: "homeworks/:id", 
            element: (
              <PermissionProtectedRoute>
                <HomeworkDetailPage />
              </PermissionProtectedRoute>
            )
          },
          // {
          //   path: "homeworks/edit/:id",
          //   element: (
          //     <PermissionProtectedRoute>
          //       <HomeworkForm />
          //     </PermissionProtectedRoute>
          //   )
          // },

          // 班级管理
          {
            path: "administrative-classes",
            element: (
              <MenuProtectedRoute
                menuId="administrative_classes_management"
              >
                <AdministrativeClassesPage />
              </MenuProtectedRoute>
            )
          },
          {
            path: "administrative-classes/:classId",
            element: (
              <MenuProtectedRoute menuId="administrative_classes_management">
                <AdministrativeClassesDetailPage />
              </MenuProtectedRoute>
            )
          },
          { 
            path: "teaching-classes", 
            element: (
              <PermissionProtectedRoute>
                <TeachingClassesPage />
              </PermissionProtectedRoute>
            )
          },
          { 
            path: "teaching-classes/:classId", 
            element: (
              <PermissionProtectedRoute>
                <TeachingClassesDetailPage />
              </PermissionProtectedRoute>
            )
          },

          // 阅卷中心
          { 
            path: "grading-center", 
            element: (
              <MenuProtectedRoute menuId="grading_center">
                <GradingCenterPage />
              </MenuProtectedRoute>
            )
          },

          // 统计分析
          { 
            path: "statistics", 
            element: (
              <PermissionProtectedRoute>
                <StatisticsPage />
              </PermissionProtectedRoute>
            )
          },

          // 学生管理
          { 
            path: "students", 
            element: (
              <CombinedProtectedRoute 
                menuId="student_management"
              >
                <StudentManagementPage />
              </CombinedProtectedRoute>
            )
          },

          // 教师管理
          { 
            path: "teachers", 
            element: (
              <CombinedProtectedRoute 
                menuId="teacher_management"
              >
                <TeacherManagementPage />
              </CombinedProtectedRoute>
            )
          },

          // 年级管理
          { 
            path: "grades", 
            element: (
              <PermissionProtectedRoute>
                <GradeManagementPage />
              </PermissionProtectedRoute>
            )
          },

          // 学科管理
          { 
            path: "subjects", 
            element: (
              <PermissionProtectedRoute>
                <SubjectManagementPage />
              </PermissionProtectedRoute>
            )
          },

          // 学科组管理
          { 
            path: "subject-groups", 
            element: (
              <PermissionProtectedRoute>
                <SubjectGroupsManagementPage />
              </PermissionProtectedRoute>
            )
          },

          // 学段管理
          { 
            path: "education-stages", 
            element: (
              <PermissionProtectedRoute>
                <EducationalStageManagementPage />
              </PermissionProtectedRoute>
            )
          },

          // 系统管理 - 需要管理员权限
          { 
            path: "tenants", 
            element: (
              <AdminProtectedRoute>
                <TenantManagementPage />
              </AdminProtectedRoute>
            )
          },
          {
            path: "casbin-policies",
            element: (
                <AdminProtectedRoute>
                  <CasbinPolicyManagementPage />
                </AdminProtectedRoute>
            )
          },
          {
            path: "menu-management",
            element: (
                <AdminProtectedRoute>
                  <MenuManagementPage />
                </AdminProtectedRoute>
            )
          },
          { 
            path: "users", 
            element: (
              <CombinedProtectedRoute 
                requireAll={false}
              >
                <UserManagementPage />
              </CombinedProtectedRoute>
            )
          },
          { 
            path: "roles", 
            element: (
              <CombinedProtectedRoute 
                menuId="role_management"
              >
                <RoleManagementPage />
              </CombinedProtectedRoute>
            )
          },
        ],
      },
    ],
  },
]);

// 保持向后兼容的默认导出
export { routerWithPermissions as router };