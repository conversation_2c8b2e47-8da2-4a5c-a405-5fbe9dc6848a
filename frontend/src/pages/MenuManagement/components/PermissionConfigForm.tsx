import React, { useState, useEffect } from 'react';
import {MenuItem} from '@/types/menu';
import RoleMultiSelect from '@/components/role/RoleMultiSelect';
import { menuRoleApi } from '@/services/menuRoleApi';
import { toast } from 'sonner';

interface PermissionFormProps {
  data: Partial<MenuItem>;
  disabled: boolean;
  onSave?: () => void;
}

const PermissionConfigForm: React.FC<PermissionFormProps> = ({
  data,
  disabled,
  onSave
}) => {
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  // 加载菜单的角色权限配置
  const loadMenuRolePermissions = async () => {
    if (!data.menu_id) return;

    try {
      setLoading(true);
      const response = await menuRoleApi.getMenuRolePermissions(data.menu_id);
      if (response.success && response.data) {
        const roleIds = response.data.roles.map(role => role.id);
        setSelectedRoles(roleIds);
      }
    } catch (error) {
      console.error('加载菜单角色权限失败:', error);
      toast.error('加载菜单角色权限失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存菜单的角色权限配置
  const saveMenuRolePermissions = async (roleIds: string[]) => {
    if (!data.menu_id) return;

    try {
      setLoading(true);
      const response = await menuRoleApi.setMenuRolePermissions(data.menu_id, {
        role_ids: roleIds
      });

      if (response.success) {
        toast.success('菜单角色权限保存成功');
        onSave?.();
      } else {
        toast.error(response.message || '保存失败');
      }
    } catch (error) {
      console.error('保存菜单角色权限失败:', error);
      toast.error('保存菜单角色权限失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理角色选择变化
  const handleRoleSelectionChange = async (roleIds: string[]) => {
    console.log(roleIds);
    setSelectedRoles(roleIds);
    // 自动保存角色权限配置到casbin
    if (!disabled) {
      await saveMenuRolePermissions(roleIds);
    }
  };

  // 组件挂载时加载角色权限配置
  useEffect(() => {
    loadMenuRolePermissions();
  }, [data.menu_id]);
  return (
    <div className="space-y-6">
      {/* 角色权限配置 */}
      <div>
        <RoleMultiSelect
          label="所需角色"
          description="选择可以访问此菜单的角色（保存到Casbin策略）"
          value={selectedRoles}
          onChange={handleRoleSelectionChange}
          disabled={disabled || loading}
          placeholder={loading ? "加载中..." : "选择角色..."}
          excludeSystemRoles={false}
          className="w-full"
        />
        {loading && (
          <div className="mt-2 text-sm text-muted-foreground">
            正在保存角色权限配置...
          </div>
        )}
      </div>
    </div>
  );
};
export default PermissionConfigForm;