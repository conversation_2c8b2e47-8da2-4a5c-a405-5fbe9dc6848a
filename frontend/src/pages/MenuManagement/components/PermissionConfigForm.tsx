import React, { useState, useEffect } from 'react';
import {MenuItem} from '@/types/menu';
import MenuRoleMultiSelect from '@/components/role/MenuRoleMultiSelect';
import { menuRoleApi } from '@/services/menuRoleApi';
import { toast } from 'sonner';

interface PermissionFormProps {
  data: Partial<MenuItem>;
  onChange: (data: Partial<MenuItem>) => void;
  disabled: boolean;
  onSave?: () => void;
}

const PermissionConfigForm: React.FC<PermissionFormProps> = ({
  data,
  onChange,
  disabled,
  onSave
}) => {
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  // 加载菜单的角色权限配置
  const loadMenuRolePermissions = async () => {
    if (!data.menu_id) return;

    try {
      setLoading(true);
      const response = await menuRoleApi.getMenuRolePermissions(data.menu_id);
      if (response.success && response.data) {
        const roleIds = response.data.roles.map(role => role.id);
        setSelectedRoles(roleIds);
      }
    } catch (error) {
      console.error('加载菜单角色权限失败:', error);
      toast.error('加载菜单角色权限失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存菜单的角色权限配置
  const saveMenuRolePermissions = async (roleIds: string[]) => {
    if (!data.menu_id) return;

    try {
      setLoading(true);
      const response = await menuRoleApi.setMenuRolePermissions(data.menu_id, {
        role_ids: roleIds
      });

      if (response.success) {
        toast.success('菜单角色权限保存成功');
        onSave?.();
      } else {
        toast.error(response.message || '保存失败');
      }
    } catch (error) {
      console.error('保存菜单角色权限失败:', error);
      toast.error('保存菜单角色权限失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理角色选择变化
  const handleRoleSelectionChange = async (roleCodes: string[]) => {
    console.log("Selected role codes:", roleCodes);
    setSelectedRoles(roleCodes);

    // 更新菜单数据的required_roles字段
    onChange({
      ...data,
      required_roles: roleCodes
    });

    // 如果菜单已存在，自动保存角色权限配置到casbin
    if (!disabled && data.menu_id) {
      // 注意：这里需要将role codes转换为role IDs来调用API
      // 暂时跳过自动保存，让用户手动保存菜单
      // await saveMenuRolePermissions(roleIds);
    }
  };

  // 组件挂载时初始化角色配置
  useEffect(() => {
    if (data.required_roles) {
      setSelectedRoles(data.required_roles);
    } else {
      setSelectedRoles([]);
    }
  }, [data.required_roles]);

  // 如果菜单已存在，加载角色权限配置
  useEffect(() => {
    if (data.menu_id) {
      loadMenuRolePermissions();
    }
  }, [data.menu_id]);

  return (
    <div className="space-y-6">
      {/* 角色权限配置 */}
      <div>
        <MenuRoleMultiSelect
          label="所需角色"
          description="选择可以访问此菜单的角色（将保存到菜单的required_roles字段）"
          value={selectedRoles}
          onChange={handleRoleSelectionChange}
          disabled={disabled || loading}
          placeholder={loading ? "加载中..." : "选择角色..."}
          excludeSystemRoles={false}
          className="w-full"
        />
        {loading && (
          <div className="mt-2 text-sm text-muted-foreground">
            正在保存角色权限配置...
          </div>
        )}
        {data.required_roles && data.required_roles.length > 0 && (
          <div className="mt-2 text-sm text-muted-foreground">
            当前配置的角色代码: {data.required_roles.join(', ')}
          </div>
        )}
      </div>
    </div>
  );
};
export default PermissionConfigForm;