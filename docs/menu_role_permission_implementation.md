# 菜单角色权限配置实现文档

## 概述

本文档描述了将前端菜单编辑的权限配置改为基于角色配置(多选)的实现，权限策略直接保存到后端的Casbin中，而不是新增数据库表字段。

## 实现方案

### 1. 架构设计

- **前端**: 使用角色多选组件替代原有的权限输入框
- **后端**: 通过Casbin策略管理角色与菜单的访问关系
- **存储**: 权限策略存储在`casbin_policies`表中，不修改`menu_permissions`表结构

### 2. 核心组件

#### 2.1 前端组件

1. **RoleMultiSelect** (`frontend/src/components/role/RoleMultiSelect.tsx`)
   - 基于角色的多选组件
   - 支持角色搜索、分类显示
   - 实时加载角色列表

2. **PermissionConfigForm** (修改)
   - 集成角色多选组件
   - 自动保存角色权限配置到Casbin
   - 实时加载和显示当前配置

#### 2.2 后端服务

1. **MenuRolePermissionService** (`backend/src/service/permission/menu_role_permission_service.rs`)
   - 管理菜单与角色的权限关系
   - 提供增删改查操作
   - 与Casbin服务集成

2. **MenuRoleController** (`backend/src/controller/permission/menu_role_controller.rs`)
   - 提供RESTful API接口
   - 权限验证和错误处理

#### 2.3 API接口

1. **menuRoleApi** (`frontend/src/services/menuRoleApi.ts`)
   - 前端API调用封装
   - 支持批量操作和错误处理

### 3. 权限策略格式

在Casbin中，菜单角色权限策略的格式为：

```
策略类型: p
主体: role_id (角色ID)
域: tenant_id (租户ID)
对象: menu:menu_id (菜单对象)
动作: access (访问动作)
效果: allow (允许)
```

示例策略：
```
p, teacher, tenant_001, menu:student_management, access, allow
p, principal, tenant_001, menu:student_management, access, allow
```

### 4. 主要功能

#### 4.1 菜单角色权限管理

- **获取菜单角色权限**: 查询指定菜单的角色配置
- **设置菜单角色权限**: 批量设置菜单的角色权限（替换模式）
- **添加角色权限**: 为菜单添加单个角色权限
- **移除角色权限**: 移除菜单的单个角色权限
- **检查角色权限**: 验证角色是否有菜单访问权限

#### 4.2 批量操作

- **批量设置**: 同时为多个菜单设置角色权限
- **复制权限**: 将一个菜单的角色权限复制到其他菜单
- **清除权限**: 清除菜单的所有角色权限

#### 4.3 权限验证

- **实时验证**: 角色选择变化时自动保存到Casbin
- **权限检查**: 支持检查用户是否有菜单访问权限
- **数据一致性**: 确保前端显示与Casbin策略一致

### 5. 使用流程

#### 5.1 配置菜单角色权限

1. 进入菜单管理页面
2. 选择要配置的菜单
3. 在权限配置区域使用角色多选组件
4. 选择可以访问该菜单的角色
5. 系统自动保存配置到Casbin策略

#### 5.2 权限验证流程

1. 用户登录系统
2. 系统获取用户的角色信息
3. 通过Casbin检查角色是否有菜单访问权限
4. 根据权限结果显示或隐藏菜单

### 6. API接口文档

#### 6.1 获取菜单角色权限

```http
GET /api/v1/permissions/menus/{menu_id}/roles
```

响应：
```json
{
  "success": true,
  "data": {
    "menu_id": "student_management",
    "roles": [
      {
        "id": "role_id_1",
        "name": "教师",
        "code": "teacher",
        "category": "business",
        "level": 8
      }
    ]
  }
}
```

#### 6.2 设置菜单角色权限

```http
PUT /api/v1/permissions/menus/{menu_id}/roles
```

请求体：
```json
{
  "role_ids": ["role_id_1", "role_id_2"]
}
```

#### 6.3 检查角色权限

```http
GET /api/v1/permissions/menus/{menu_id}/roles/{role_id}/check
```

响应：
```json
{
  "success": true,
  "data": true
}
```

### 7. 数据库影响

- **无需修改表结构**: 不在`menu_permissions`表中添加新字段
- **使用现有Casbin表**: 权限策略存储在`casbin_policies`表中
- **保持数据一致性**: 通过Casbin确保权限数据的一致性

### 8. 优势

1. **灵活性**: 基于Casbin的策略管理，支持复杂的权限逻辑
2. **可扩展性**: 易于扩展新的权限类型和验证规则
3. **一致性**: 统一的权限管理机制
4. **性能**: Casbin的高效权限检查
5. **维护性**: 清晰的代码结构和API设计

### 9. 测试

验证功能：

- 角色列表加载测试
- 菜单角色权限获取测试
- 菜单角色权限设置测试
- 单个角色权限添加测试
- 角色权限检查测试

### 10. 部署注意事项

1. 确保Casbin服务正常运行
2. 验证角色数据的完整性
3. 测试权限策略的正确性
4. 监控API接口的性能

## 总结

通过这个实现，我们成功地将菜单权限配置从基于权限字符串的模式改为基于角色的多选配置模式，同时保持了系统的灵活性和可扩展性。所有权限策略都通过Casbin进行管理，确保了权限验证的一致性和高效性。
