-- 修复行政班管理菜单权限配置
-- 解决 class_teacher 角色与 menu:access 权限不匹配的问题
-- 执行时间: 2025-08-09

-- ================================================================
-- 1. 更新 administrative_classes_management 菜单的 required_roles 配置
-- ================================================================

-- 确保菜单权限表有 required_roles 字段
ALTER TABLE public.menu_permissions 
ADD COLUMN IF NOT EXISTS required_roles TEXT[];

-- 更新行政班管理菜单的角色要求
UPDATE public.menu_permissions 
SET required_roles = ARRAY['class_teacher', 'principal', 'academic_director']
WHERE menu_id = 'administrative_classes_management';

-- 同时更新其他相关菜单的角色配置
UPDATE public.menu_permissions 
SET required_roles = ARRAY['class_teacher', 'subject_teacher', 'principal']
WHERE menu_id = 'student_management';

UPDATE public.menu_permissions 
SET required_roles = ARRAY['class_teacher', 'subject_teacher', 'principal']
WHERE menu_id = 'grade_score_management';

-- ================================================================
-- 2. 确保 Casbin 策略中有正确的菜单访问权限
-- ================================================================

-- 为班主任角色添加行政班管理菜单访问权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
VALUES ('p', 'role:class_teacher', '*', 'menu:administrative_classes_management', 'access', 'allow', 'menu_access')
ON CONFLICT (ptype, v0, v1, v2, v3, v4) DO NOTHING;

-- 为班主任角色添加学生管理菜单访问权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
VALUES ('p', 'role:class_teacher', '*', 'menu:student_management', 'access', 'allow', 'menu_access')
ON CONFLICT (ptype, v0, v1, v2, v3, v4) DO NOTHING;

-- 为班主任角色添加成绩管理菜单访问权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
VALUES ('p', 'role:class_teacher', '*', 'menu:grade_score_management', 'access', 'allow', 'menu_access')
ON CONFLICT (ptype, v0, v1, v2, v3, v4) DO NOTHING;

-- ================================================================
-- 3. 添加班主任的数据权限策略
-- ================================================================

-- 班主任对行政班的读取权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
VALUES ('p', 'role:class_teacher', '*', 'administrative_class:*', 'read', 'allow', 'data_access')
ON CONFLICT (ptype, v0, v1, v2, v3, v4) DO NOTHING;

-- 班主任对班级的管理权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
VALUES ('p', 'role:class_teacher', '*', 'class:*', 'read', 'allow', 'data_access')
ON CONFLICT (ptype, v0, v1, v2, v3, v4) DO NOTHING;

INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
VALUES ('p', 'role:class_teacher', '*', 'class:*', 'manage', 'allow', 'data_access')
ON CONFLICT (ptype, v0, v1, v2, v3, v4) DO NOTHING;

-- ================================================================
-- 4. 验证修复结果
-- ================================================================

-- 检查菜单权限配置
SELECT 
    '=== 菜单权限配置检查 ===' as section,
    menu_id,
    name,
    required_roles
FROM public.menu_permissions 
WHERE menu_id IN ('administrative_classes_management', 'student_management', 'grade_score_management')
ORDER BY menu_id;

-- 检查 Casbin 菜单访问策略
SELECT 
    '=== Casbin 菜单访问策略检查 ===' as section,
    v0 as role,
    v2 as menu_object,
    v3 as action,
    v4 as effect
FROM public.casbin_policies 
WHERE ptype = 'p' 
  AND v0 = 'role:class_teacher' 
  AND v2 LIKE 'menu:%'
ORDER BY v2;

-- 检查数据权限策略
SELECT 
    '=== Casbin 数据权限策略检查 ===' as section,
    v0 as role,
    v2 as resource_object,
    v3 as action,
    v4 as effect
FROM public.casbin_policies 
WHERE ptype = 'p' 
  AND v0 = 'role:class_teacher' 
  AND v2 LIKE '%class%'
ORDER BY v2;

-- 统计信息
SELECT 
    '=== 统计信息 ===' as section,
    COUNT(*) as total_class_teacher_policies
FROM public.casbin_policies 
WHERE ptype = 'p' AND v0 = 'role:class_teacher';
