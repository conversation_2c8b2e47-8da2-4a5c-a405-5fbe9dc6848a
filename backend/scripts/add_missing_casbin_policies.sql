-- 添加缺失的Casbin策略
-- 为班主任角色添加菜单访问权限

-- 检查是否已存在策略，如果不存在则添加
DO $$
BEGIN
    -- 为班主任角色添加行政班管理菜单访问权限
    IF NOT EXISTS (
        SELECT 1 FROM public.casbin_policies 
        WHERE ptype = 'p' AND v0 = 'role:class_teacher' 
        AND v2 = 'menu:administrative_classes_management' AND v3 = 'access'
    ) THEN
        INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
        VALUES ('p', 'role:class_teacher', '*', 'menu:administrative_classes_management', 'access', 'allow', 'menu_access');
    END IF;

    -- 为班主任角色添加学生管理菜单访问权限
    IF NOT EXISTS (
        SELECT 1 FROM public.casbin_policies 
        WHERE ptype = 'p' AND v0 = 'role:class_teacher' 
        AND v2 = 'menu:student_management' AND v3 = 'access'
    ) THEN
        INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
        VALUES ('p', 'role:class_teacher', '*', 'menu:student_management', 'access', 'allow', 'menu_access');
    END IF;

    -- 为班主任角色添加成绩管理菜单访问权限
    IF NOT EXISTS (
        SELECT 1 FROM public.casbin_policies 
        WHERE ptype = 'p' AND v0 = 'role:class_teacher' 
        AND v2 = 'menu:grade_score_management' AND v3 = 'access'
    ) THEN
        INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
        VALUES ('p', 'role:class_teacher', '*', 'menu:grade_score_management', 'access', 'allow', 'menu_access');
    END IF;

    -- 班主任对行政班的读取权限
    IF NOT EXISTS (
        SELECT 1 FROM public.casbin_policies 
        WHERE ptype = 'p' AND v0 = 'role:class_teacher' 
        AND v2 = 'administrative_class:*' AND v3 = 'read'
    ) THEN
        INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
        VALUES ('p', 'role:class_teacher', '*', 'administrative_class:*', 'read', 'allow', 'data_access');
    END IF;

    -- 班主任对班级的读取权限
    IF NOT EXISTS (
        SELECT 1 FROM public.casbin_policies 
        WHERE ptype = 'p' AND v0 = 'role:class_teacher' 
        AND v2 = 'class:*' AND v3 = 'read'
    ) THEN
        INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
        VALUES ('p', 'role:class_teacher', '*', 'class:*', 'read', 'allow', 'data_access');
    END IF;

    -- 班主任对班级的管理权限
    IF NOT EXISTS (
        SELECT 1 FROM public.casbin_policies 
        WHERE ptype = 'p' AND v0 = 'role:class_teacher' 
        AND v2 = 'class:*' AND v3 = 'manage'
    ) THEN
        INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
        VALUES ('p', 'role:class_teacher', '*', 'class:*', 'manage', 'allow', 'data_access');
    END IF;
END $$;

-- 验证添加的策略
SELECT 
    '=== 新增的菜单访问策略 ===' as section,
    v0 as role,
    v2 as menu_object,
    v3 as action,
    v4 as effect
FROM public.casbin_policies 
WHERE ptype = 'p' 
  AND v0 = 'role:class_teacher' 
  AND v2 IN ('menu:administrative_classes_management', 'menu:student_management', 'menu:grade_score_management')
ORDER BY v2;
