-- 测试菜单权限修复效果
-- 验证班主任角色是否能正确访问行政班管理菜单

-- ================================================================
-- 1. 检查菜单权限配置
-- ================================================================

SELECT 
    '=== 菜单权限配置验证 ===' as section,
    menu_id,
    name,
    required_roles,
    is_active
FROM public.menu_permissions 
WHERE menu_id = 'administrative_classes_management';

-- ================================================================
-- 2. 检查Casbin策略配置
-- ================================================================

SELECT 
    '=== Casbin菜单访问策略验证 ===' as section,
    ptype,
    v0 as subject,
    v1 as domain,
    v2 as object,
    v3 as action,
    v4 as effect,
    v5 as metadata
FROM public.casbin_policies 
WHERE ptype = 'p' 
  AND v0 = 'role:class_teacher' 
  AND v2 = 'menu:administrative_classes_management'
  AND v3 = 'access';

-- ================================================================
-- 3. 检查班主任的完整菜单权限
-- ================================================================

SELECT 
    '=== 班主任完整菜单权限列表 ===' as section,
    v2 as menu_object,
    v3 as action,
    v4 as effect
FROM public.casbin_policies 
WHERE ptype = 'p' 
  AND v0 = 'role:class_teacher' 
  AND v2 LIKE 'menu:%'
ORDER BY v2;

-- ================================================================
-- 4. 检查班主任的数据权限
-- ================================================================

SELECT 
    '=== 班主任数据权限列表 ===' as section,
    v2 as resource_object,
    v3 as action,
    v4 as effect
FROM public.casbin_policies 
WHERE ptype = 'p' 
  AND v0 = 'role:class_teacher' 
  AND v2 NOT LIKE 'menu:%'
ORDER BY v2;

-- ================================================================
-- 5. 模拟权限检查
-- ================================================================

-- 检查班主任是否有访问行政班管理菜单的权限
WITH permission_check AS (
    SELECT 
        CASE 
            WHEN EXISTS (
                SELECT 1 FROM public.casbin_policies 
                WHERE ptype = 'p' 
                  AND v0 = 'role:class_teacher' 
                  AND v2 = 'menu:administrative_classes_management' 
                  AND v3 = 'access' 
                  AND v4 = 'allow'
            ) THEN '✅ 有权限'
            ELSE '❌ 无权限'
        END as menu_access_result
)
SELECT 
    '=== 权限检查模拟结果 ===' as section,
    'role:class_teacher' as role,
    'menu:administrative_classes_management' as menu,
    'access' as action,
    menu_access_result as result
FROM permission_check;

-- ================================================================
-- 6. 检查角色配置是否正确
-- ================================================================

SELECT 
    '=== 角色配置验证 ===' as section,
    id,
    name,
    code,
    category,
    level,
    is_active
FROM public.roles 
WHERE code = 'class_teacher';

-- ================================================================
-- 7. 统计信息
-- ================================================================

SELECT 
    '=== 统计信息 ===' as section,
    'class_teacher菜单权限数量' as metric,
    COUNT(*) as count
FROM public.casbin_policies 
WHERE ptype = 'p' 
  AND v0 = 'role:class_teacher' 
  AND v2 LIKE 'menu:%'

UNION ALL

SELECT 
    '=== 统计信息 ===' as section,
    'class_teacher数据权限数量' as metric,
    COUNT(*) as count
FROM public.casbin_policies 
WHERE ptype = 'p' 
  AND v0 = 'role:class_teacher' 
  AND v2 NOT LIKE 'menu:%'

UNION ALL

SELECT 
    '=== 统计信息 ===' as section,
    'class_teacher总权限数量' as metric,
    COUNT(*) as count
FROM public.casbin_policies 
WHERE ptype = 'p' 
  AND v0 = 'role:class_teacher';
